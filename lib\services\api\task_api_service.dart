import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/task_type_models.dart';
import '../../models/task_models.dart';
import 'api_service.dart';

/// خدمة API للمهام
class TaskApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    try {
      debugPrint('🔄 بدء طلب تحميل المهام من API...');

      // إضافة معاملات للحد من البيانات المحملة وتضمين المرفقات
      final queryParams = {
        'page': '1',
        'pageSize': '50', // تحديد عدد المهام المحملة
        'include': 'attachments,comments,subtasks', // تضمين المرفقات والتعليقات والمهام الفرعية
      };

      final response = await _apiService.get('/api/Tasks', queryParams: queryParams);
      debugPrint('✅ تم استلام استجابة API للمهام - Status: ${response.statusCode}');
      // debugPrint('📄 محتوى الاستجابة: ${response.body.substring(0, response.body.length > 500 ? 500 : response.body.length)}...');
      
      // فحص إذا كانت البيانات تحتوي على مرفقات
      if (response.body.contains('attachments')) {
        debugPrint('🔗 تم العثور على مرفقات في استجابة API');
      } else {
        debugPrint('⚠️ لا توجد مرفقات في استجابة API');
      }

      final tasks = _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );

      debugPrint('✅ تم تحليل ${tasks.length} مهمة بنجاح');

      // تحميل صلاحيات الوصول لكل مهمة (اختياري - يمكن تعطيله لتحسين الأداء)
      // for (int i = 0; i < tasks.length; i++) {
      //   try {
      //     final accessResponse = await _apiService.get('/api/TaskAccess/task/${tasks[i].id}');
      //     final accessUsers = _apiService.handleListResponse<Map<String, dynamic>>(
      //       accessResponse,
      //       (json) => json,
      //     );
      //
      //     final accessUserIds = accessUsers
      //         .map((user) => user['userId']?.toString())
      //         .where((id) => id != null)
      //         .cast<String>()
      //         .toList();
      //
      //     tasks[i] = tasks[i].copyWith(accessUserIds: accessUserIds);
      //   } catch (e) {
      //     debugPrint('تحذير: لا يمكن تحميل صلاحيات الوصول للمهمة ${tasks[i].id}: $e');
      //   }
      // }

      return tasks;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المهام: $e');
      debugPrint('📍 نوع الخطأ: ${e.runtimeType}');

      // في حالة خطأ الخادم، نحاول مرة أخرى بمعاملات مبسطة
      if (e is ApiException && e.statusCode == 500) {
        debugPrint('🔄 محاولة إعادة الطلب بمعاملات مبسطة...');
        try {
          await Future.delayed(const Duration(seconds: 2));

          // طلب مبسط بدون معاملات إضافية
          final retryResponse = await _apiService.get('/api/Tasks?pageSize=10');
          debugPrint('✅ نجحت إعادة المحاولة - Status: ${retryResponse.statusCode}');

          return _apiService.handleListResponse<Task>(
            retryResponse,
            (json) => Task.fromJson(json),
          );
        } catch (retryError) {
          debugPrint('❌ فشلت إعادة المحاولة: $retryError');
          // إرجاع قائمة فارغة بدلاً من رمي الخطأ
          return [];
        }
      }

      rethrow;
    }
  }

  /// الحصول على مهمة بواسطة المعرف مع تحميل صلاحيات الوصول
  Future<Task?> getTaskById(int id, {bool forceRefresh = false}) async {
    try {
      // إضافة معاملات لتحميل جميع العلاقات بما في ذلك المرفقات
      final queryParams = {
        'include': 'attachments,comments,subtasks,creator,assignee,department,taskType',
        if (forceRefresh) '_ts': DateTime.now().millisecondsSinceEpoch.toString(),
      };
      final response = await _apiService.get('/api/Tasks/$id', queryParams: queryParams);
      final task = _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );

      // تحميل صلاحيات الوصول للمهمة
      // تم تعطيل هذا الكود مؤقتاً حتى يتم تفعيل نظام صلاحيات الوصول بالكامل
      // if (task != null) {
      //   try {
      //     final accessResponse = await _apiService.get('/api/TaskAccess/task/$id');
      //     final accessUsers = _apiService.handleListResponse<Map<String, dynamic>>(
      //       accessResponse,
      //       (json) => json,
      //     );

      //     // تحويل إلى قائمة معرفات المستخدمين
      //     final accessUserIds = accessUsers
      //         .map((user) => user['userId']?.toString())
      //         .where((id) => id != null)
      //         .cast<String>()
      //         .toList();

      //     // إنشاء نسخة محدثة من المهمة مع صلاحيات الوصول
      //     return task.copyWith(accessUserIds: accessUserIds);
      //   } catch (e) {
      //     debugPrint('تحذير: لا يمكن تحميل صلاحيات الوصول للمهمة $id: $e');
      //     // إرجاع المهمة بدون صلاحيات الوصول
      //     return task;
      //   }
      // }

      return task;
    } catch (e) {
      debugPrint('خطأ في الحصول على المهمة: $e');
      return null;
    }
  }

  /// إنشاء مهمة جديدة
  Future<Task?> createTask(Task task) async {
    try {
      debugPrint('🚀 إرسال طلب إنشاء مهمة إلى: /api/Tasks');
      debugPrint('📦 بيانات المهمة: ${task.toJson()}');

      final response = await _apiService.post(
        '/api/Tasks',
        task.toJson(),
      );

      debugPrint('📡 استجابة الخادم - الحالة: ${response.statusCode}');
      // debugPrint('📄 محتوى الاستجابة: ${response.body}');

      if (response.statusCode == 201) {
        final createdTask = _apiService.handleResponse<Task>(
          response,
          (json) => Task.fromJson(json),
        );
        debugPrint('✅ تم إنشاء المهمة بنجاح في API');

        // إضافة صلاحيات الوصول الإضافية إذا كانت موجودة
        // تم تعطيل هذا الكود مؤقتاً حتى يتم تفعيل نظام صلاحيات الوصول بالكامل
        // if (task.accessUserIds != null && task.accessUserIds!.isNotEmpty) {
        //   try {
        //     final accessUserIds = task.accessUserIds!
        //         .map((id) => int.tryParse(id))
        //         .where((id) => id != null)
        //         .cast<int>()
        //         .toList();
        //
        //     if (accessUserIds.isNotEmpty) {
        //       final accessResponse = await _apiService.put(
        //         '/api/TaskAccess/task/${createdTask.id}',
        //         {'userIds': accessUserIds},
        //       );
        //
        //       if (accessResponse.statusCode >= 200 && accessResponse.statusCode < 300) {
        //         debugPrint('✅ تم إضافة صلاحيات الوصول للمهمة الجديدة');
        //       } else {
        //         debugPrint('⚠️ فشل في إضافة صلاحيات الوصول للمهمة الجديدة');
        //       }
        //     }
        //   } catch (e) {
        //     debugPrint('⚠️ خطأ في إضافة صلاحيات الوصول للمهمة الجديدة: $e');
        //   }
        // }

        return createdTask;
      } else {
        debugPrint('❌ فشل في إنشاء المهمة - رمز الحالة: ${response.statusCode}');
        debugPrint('📄 رسالة الخطأ: ${response.body}');

        // محاولة استخراج رسالة خطأ مفصلة
        try {
          final errorData = jsonDecode(response.body);
          if (errorData is Map<String, dynamic>) {
            debugPrint('🔍 تفاصيل الخطأ: $errorData');
          }
        } catch (e) {
          debugPrint('⚠️ لا يمكن تحليل رسالة الخطأ: $e');
        }

        return _apiService.handleResponse<Task>(
          response,
          (json) => Task.fromJson(json),
        );
      }
    } catch (e) {
      debugPrint('💥 خطأ في إنشاء المهمة: $e');
      debugPrint('🔍 نوع الخطأ: ${e.runtimeType}');
      rethrow;
    }
  }

  /// تحديث مهمة
  Future<Task?> updateTask(Task task) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/${task.id}',
        task.toJson(),
      );

      // التعامل مع الاستجابة 204 No Content
      if (response.statusCode == 204) {
        debugPrint('✅ تم تحديث المهمة بنجاح (204 No Content)');

        // تحديث صلاحيات الوصول إذا كانت موجودة
        // تم تعطيل هذا الكود مؤقتاً حتى يتم تفعيل نظام صلاحيات الوصول بالكامل
        // if (task.accessUserIds != null) {
        //   try {
        //     final accessUserIds = task.accessUserIds!
        //         .map((id) => int.tryParse(id))
        //         .where((id) => id != null)
        //         .cast<int>()
        //         .toList();
        //
        //     final accessResponse = await _apiService.put(
        //       '/api/TaskAccess/task/${task.id}',
        //       {'userIds': accessUserIds},
        //     );
        //
        //     if (accessResponse.statusCode >= 200 && accessResponse.statusCode < 300) {
        //       debugPrint('✅ تم تحديث صلاحيات الوصول للمهمة');
        //     } else {
        //       debugPrint('⚠️ فشل في تحديث صلاحيات الوصول للمهمة');
        //     }
        //   } catch (e) {
        //     debugPrint('⚠️ خطأ في تحديث صلاحيات الوصول للمهمة: $e');
        //   }
        // }

        // إرجاع المهمة المحدثة محلياً
        return task;
      }

      // للاستجابات الأخرى، محاولة تحليل JSON
      if (response.body.isNotEmpty) {
        return _apiService.handleResponse<Task>(
          response,
          (json) => Task.fromJson(json),
        );
      }

      // إذا كانت الاستجابة فارغة ولكن ناجحة، إرجاع المهمة المحدثة
      return task;
    } catch (e) {
      debugPrint('خطأ في تحديث المهمة: $e');
      rethrow;
    }
  }
  
  /// تحويل المهمة إلى مستخدم آخر
  Future<Task?> transferTask(
    int taskId,
    int newAssigneeId,
    int currentUserId,
    String comment,
    List<String> attachments,
  ) async {
    try {
      debugPrint('🔄 تحويل المهمة $taskId إلى المستخدم $newAssigneeId');
      
      final requestBody = {
        'newAssigneeId': newAssigneeId,
        'currentUserId': currentUserId,
        'comment': comment,
        'attachments': attachments,
      };
      
      final response = await _apiService.post(
        '/api/Tasks/$taskId/transfer',
        requestBody,
      );
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        debugPrint('✅ تم تحويل المهمة بنجاح');
        
        return _apiService.handleResponse<Task>(
          response,
          (json) => Task.fromJson(json),
        );
      } else {
        debugPrint('❌ فشل في تحويل المهمة: ${response.statusCode} - ${response.body}');
        throw Exception('فشل في تحويل المهمة: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحويل المهمة: $e');
      rethrow;
    }
  }

  /// حذف مهمة (حذف ناعم)
  Future<bool> deleteTask(int id) async {
    try {
      final response = await _apiService.delete('/api/Tasks/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    }
  }

  /// الحصول على المهام بحسب المكلف بها
  Future<List<Task>> getTasksByAssignee(int assigneeId, {Map<String, dynamic>? queryParams, bool forceRefresh = false}) async {
    try {
      // إضافة معامل include للحصول على المرفقات
      final finalQueryParams = {
        'include': 'attachments,comments,subtasks',
        ...?queryParams,
      };
      // إذا كان forceRefresh = true يمكن إضافة منطق لتجاهل الكاش هنا إذا كان هناك كاش
      // أو فقط جلب البيانات من السيرفر كما هو الحال هنا
      final response = await _apiService.get('/api/Tasks/assignee/$assigneeId', queryParams: finalQueryParams);
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام المكلف $assigneeId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام التي يمكن للمستخدم الوصول إليها من جدول task_access_users - محسن مع التخزين المؤقت
  Future<List<Task>> getTasksByUserAccess(int userId, {Map<String, dynamic>? queryParams, bool forceRefresh = false}) async {
    try {
      // إضافة معامل include للحصول على المرفقات
      final finalQueryParams = {
        'include': 'attachments,comments,subtasks',
        ...?queryParams,
        // 🚀 التحسين: إضافة timestamp عند forceRefresh لتجاهل cache
        if (forceRefresh) '_ts': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      debugPrint('🔄 جلب المهام التي يمكن للمستخدم $userId الوصول إليها - forceRefresh: $forceRefresh');
      final response = await _apiService.get('/api/Tasks/user-access/$userId', queryParams: finalQueryParams);
      final tasks = _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );

      debugPrint('✅ تم جلب ${tasks.length} مهمة يمكن للمستخدم الوصول إليها');
      return tasks;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المهام التي يمكن للمستخدم $userId الوصول إليها: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب المنشئ
  Future<List<Task>> getTasksByCreator(int creatorId) async {
    try {
      // إضافة معامل include للحصول على المرفقات
      final queryParams = {
        'include': 'attachments,comments,subtasks',
      };
      
      final response = await _apiService.get('/api/Tasks/creator/$creatorId', queryParams: queryParams);
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام المنشئ $creatorId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب القسم - محسن مع دعم forceRefresh
  Future<List<Task>> getTasksByDepartment(int departmentId, {bool forceRefresh = false}) async {
    try {
      // إضافة معامل include للحصول على المرفقات
      final queryParams = {
        'include': 'attachments,comments,subtasks',
        // 🚀 التحسين: إضافة timestamp عند forceRefresh لتجاهل cache
        if (forceRefresh) '_ts': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      debugPrint('🔄 جلب مهام القسم $departmentId - forceRefresh: $forceRefresh');
      // تعديل المسار ليطابق الباك اند
      final response = await _apiService.get('/api/Departments/$departmentId/tasks', queryParams: queryParams);
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام القسم $departmentId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب الحالة
  Future<List<Task>> getTasksByStatus(String status) async {
    try {
      final response = await _apiService.get('/api/Tasks/status/$status');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام الحالة $status: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب الأولوية
  Future<List<Task>> getTasksByPriority(String priority) async {
    try {
      final response = await _apiService.get('/api/Tasks/priority/$priority');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام الأولوية $priority: $e');
      rethrow;
    }
  }

  /// البحث في المهام
  Future<List<Task>> searchTasks(String query) async {
    try {
      final response = await _apiService.get('/api/Tasks/search', queryParams: {'q': query});
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في المهام: $e');
      return [];
    }
  }

  /// الحصول على المهام المتأخرة
  Future<List<Task>> getOverdueTasks() async {
    try {
      debugPrint('🔄 بدء طلب تحميل المهام المتأخرة من API...');
      final response = await _apiService.get('/api/Tasks/overdue');
      final tasks = _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
      debugPrint('✅ تم تحميل ${tasks.length} مهمة متأخرة');
      return tasks;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المهام المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على المهام المكتملة
  Future<List<Task>> getCompletedTasks() async {
    try {
      final response = await _apiService.get('/api/Tasks/completed');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام المكتملة: $e');
      return [];
    }
  }

  /// الحصول على المهام المقتربة من الموعد النهائي (48 ساعة أو أقل)
  Future<List<Task>> getTasksDueSoon() async {
    try {
      debugPrint('🔄 بدء طلب تحميل المهام المقتربة من الموعد النهائي من API...');
      final response = await _apiService.get('/api/Tasks/due-soon');
      final tasks = _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
      debugPrint('✅ تم تحميل ${tasks.length} مهمة مقتربة من الموعد النهائي');
      return tasks;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المهام المقتربة من الموعد النهائي: $e');
      return [];
    }
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(int taskId, String status) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/$taskId/status',
        {'status': status},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      return false;
    }
  }

  /// تحديث نسبة إنجاز المهمة
  Future<bool> updateTaskProgress(int taskId, int percentage) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/$taskId/progress',
        {'completionPercentage': percentage},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث نسبة إنجاز المهمة: $e');
      return false;
    }
  }

  // تم تعطيل جميع دوال إدارة الحالات والأولويات من خلال API لأن الإدارة أصبحت محلية فقط

  // Future<List<TaskStatus>> getTaskStatuses() async => throw UnimplementedError('TaskStatus management is now handled on the frontend.');
  // Future<TaskStatus?> createTaskStatus(TaskStatus status) async => throw UnimplementedError('TaskStatus management is now handled on the frontend.');
  // Future<TaskStatus?> updateTaskStatusModel(TaskStatus status) async => throw UnimplementedError('TaskStatus management is now handled on the frontend.');
  // Future<bool> deleteTaskStatus(int id) async => throw UnimplementedError('TaskStatus management is now handled on the frontend.');

  // Future<List<TaskPriority>> getTaskPriorities() async => throw UnimplementedError('TaskPriority management is now handled on the frontend.');
  // Future<TaskPriority?> createTaskPriority(TaskPriority priority) async => throw UnimplementedError('TaskPriority management is now handled on the frontend.');
  // Future<TaskPriority?> updateTaskPriority(TaskPriority priority) async => throw UnimplementedError('TaskPriority management is now handled on the frontend.');
  // Future<bool> deleteTaskPriority(int id) async => throw UnimplementedError('TaskPriority management is now handled on the frontend.');

  /// الحصول على جميع أنواع المهام
  Future<List<TaskType>> getTaskTypes() async {
    try {
      final response = await _apiService.get('/api/TaskTypes');
      return _apiService.handleListResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع المهام: $e');
      rethrow;
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<TaskType?> createTaskType(TaskType type) async {
    try {
      final response = await _apiService.post(
        '/api/TaskTypes',
        type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      rethrow;
    }
  }

  /// تحديث نوع مهمة
  Future<TaskType?> updateTaskType(TaskType type) async {
    try {
      final response = await _apiService.put(
        '/api/TaskTypes/${type.id}',
        type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث نوع المهمة: $e');
      rethrow;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteTaskType(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskTypes/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف نوع المهمة: $e');
      return false;
    }
  }
}
